<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js 诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .diagnostic-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 Chart.js 雷达图诊断工具</h1>
    
    <div class="diagnostic-card">
        <h2>📊 Chart.js 加载状态检测</h2>
        <div id="chartjs-status" class="status info">正在检测...</div>
        <div id="chartjs-details"></div>
    </div>

    <div class="diagnostic-card">
        <h2>🌐 网络连接测试</h2>
        <button onclick="testCDNConnections()">测试 CDN 连接</button>
        <div id="network-results"></div>
    </div>

    <div class="diagnostic-card">
        <h2>🎯 雷达图创建测试</h2>
        <button onclick="testRadarChart()">测试雷达图创建</button>
        <div id="radar-test-container">
            <canvas id="testCanvas" width="300" height="300" style="border: 1px solid #ddd; margin: 10px 0;"></canvas>
        </div>
        <div id="radar-results"></div>
    </div>

    <div class="diagnostic-card">
        <h2>🔧 修复建议</h2>
        <div id="fix-suggestions"></div>
    </div>

    <script>
        let diagnosticResults = {
            chartjsLoaded: false,
            cdnAccessible: false,
            radarCreated: false,
            errors: []
        };

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function addResult(containerId, content) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = 'test-result';
            div.innerHTML = content;
            container.appendChild(div);
        }

        // 检测 Chart.js 加载状态
        function checkChartJS() {
            const details = document.getElementById('chartjs-details');
            
            if (typeof Chart === 'undefined') {
                updateStatus('chartjs-status', '❌ Chart.js 未加载', 'error');
                details.innerHTML = `
                    <div class="test-result">
                        <strong>问题：</strong>Chart.js 库未能成功加载<br>
                        <strong>可能原因：</strong>
                        <ul>
                            <li>网络连接问题</li>
                            <li>CDN 服务不可用</li>
                            <li>脚本加载顺序问题</li>
                            <li>浏览器阻止了外部脚本</li>
                        </ul>
                    </div>
                `;
                diagnosticResults.errors.push('Chart.js 未加载');
                return false;
            }

            updateStatus('chartjs-status', '✅ Chart.js 已成功加载', 'success');
            details.innerHTML = `
                <div class="test-result">
                    <strong>Chart.js 版本：</strong>${Chart.version || '未知'}<br>
                    <strong>可用图表类型：</strong>${Object.keys(Chart.registry.controllers).join(', ')}<br>
                    <strong>加载时间：</strong>${performance.now().toFixed(2)}ms
                </div>
            `;
            diagnosticResults.chartjsLoaded = true;
            return true;
        }

        // 测试 CDN 连接
        async function testCDNConnections() {
            const cdnUrls = [
                'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js',
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js'
            ];

            const results = document.getElementById('network-results');
            results.innerHTML = '<div class="status info">正在测试 CDN 连接...</div>';

            for (const url of cdnUrls) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(url, { method: 'HEAD' });
                    const endTime = performance.now();
                    
                    if (response.ok) {
                        addResult('network-results', `
                            ✅ <strong>${url}</strong><br>
                            状态: ${response.status} ${response.statusText}<br>
                            响应时间: ${(endTime - startTime).toFixed(2)}ms
                        `);
                        diagnosticResults.cdnAccessible = true;
                    } else {
                        addResult('network-results', `
                            ❌ <strong>${url}</strong><br>
                            状态: ${response.status} ${response.statusText}
                        `);
                    }
                } catch (error) {
                    addResult('network-results', `
                        ❌ <strong>${url}</strong><br>
                        错误: ${error.message}
                    `);
                    diagnosticResults.errors.push(`CDN 连接失败: ${error.message}`);
                }
            }
        }

        // 测试雷达图创建
        function testRadarChart() {
            const canvas = document.getElementById('testCanvas');
            const results = document.getElementById('radar-results');
            
            if (!diagnosticResults.chartjsLoaded) {
                updateStatus('radar-results', '❌ 无法测试：Chart.js 未加载', 'error');
                return;
            }

            try {
                // 清除之前的图表
                if (window.testChart) {
                    window.testChart.destroy();
                }

                const ctx = canvas.getContext('2d');
                window.testChart = new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: ['销售管理', '项目管理', '技术理解', '团队领导', '客户关系', '战略规划'],
                        datasets: [{
                            label: '技能水平',
                            data: [95, 90, 85, 92, 96, 88],
                            backgroundColor: 'rgba(37, 99, 235, 0.2)',
                            borderColor: 'rgba(37, 99, 235, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(245, 158, 11, 1)',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 6
                        }]
                    },
                    options: {
                        responsive: false,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            r: {
                                beginAtZero: true,
                                max: 100,
                                ticks: {
                                    stepSize: 20,
                                    color: '#6b7280',
                                    font: { size: 10 }
                                },
                                grid: { color: 'rgba(107, 114, 128, 0.3)' },
                                pointLabels: {
                                    color: '#374151',
                                    font: { size: 11, weight: '600' }
                                }
                            }
                        }
                    }
                });

                addResult('radar-results', '✅ 雷达图创建成功！图表已显示在上方画布中。');
                diagnosticResults.radarCreated = true;
                
            } catch (error) {
                addResult('radar-results', `❌ 雷达图创建失败: ${error.message}`);
                diagnosticResults.errors.push(`雷达图创建失败: ${error.message}`);
            }
        }

        // 生成修复建议
        function generateFixSuggestions() {
            const container = document.getElementById('fix-suggestions');
            let suggestions = [];

            if (!diagnosticResults.chartjsLoaded) {
                suggestions.push(`
                    <h3>🔧 Chart.js 加载问题修复</h3>
                    <div class="code-block">
<!-- 方案1: 使用多个 CDN 备用 -->
&lt;script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js" 
        onerror="loadChartJSFallback()"&gt;&lt;/script&gt;
&lt;script&gt;
function loadChartJSFallback() {
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js';
    document.head.appendChild(script);
}
&lt;/script&gt;

<!-- 方案2: 本地备用文件 -->
&lt;script src="./js/chart.min.js"&gt;&lt;/script&gt;
                    </div>
                `);
            }

            if (!diagnosticResults.radarCreated) {
                suggestions.push(`
                    <h3>📊 雷达图创建问题修复</h3>
                    <div class="code-block">
// 添加延迟加载和错误处理
function initSkillsChart() {
    const ctx = document.getElementById('skillsRadarChart');
    if (!ctx) return;

    // 等待 Chart.js 加载
    function waitForChart() {
        return new Promise((resolve) => {
            if (typeof Chart !== 'undefined') {
                resolve(true);
                return;
            }
            
            let attempts = 0;
            const checkInterval = setInterval(() => {
                if (typeof Chart !== 'undefined') {
                    clearInterval(checkInterval);
                    resolve(true);
                } else if (++attempts > 20) {
                    clearInterval(checkInterval);
                    resolve(false);
                }
            }, 250);
        });
    }

    waitForChart().then(chartLoaded => {
        if (chartLoaded) {
            // 创建图表代码...
        } else {
            // 显示备用静态图表
        }
    });
}
                    </div>
                `);
            }

            if (diagnosticResults.errors.length > 0) {
                suggestions.push(`
                    <h3>⚠️ 发现的问题</h3>
                    <ul>
                        ${diagnosticResults.errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                `);
            }

            if (suggestions.length === 0) {
                suggestions.push('<div class="status success">✅ 所有测试通过，没有发现问题！</div>');
            }

            container.innerHTML = suggestions.join('');
        }

        // 页面加载完成后自动运行诊断
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkChartJS();
                generateFixSuggestions();
            }, 1000);
        });
    </script>
</body>
</html>
