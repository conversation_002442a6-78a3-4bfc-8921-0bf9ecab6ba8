<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="翟生 - 政企数字化销售专家，11年经验，专注AIoT技术商业化落地">
    <meta name="keywords" content="销售总监,数字化,AI,教育信息化,新能源,项目管理">
    <meta name="author" content="翟生">
    <title>翟生 - 专业交互式简历</title>
    <script src="https://cdn.tailwindcss.com" onerror="console.warn('Tailwind CSS 加载失败')"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js" onerror="console.warn('Chart.js 加载失败，尝试备用CDN'); loadChartJSFallback()"></script>
    <script>
        // Chart.js备用加载函数
        function loadChartJSFallback() {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js';
            script.onerror = function() {
                console.warn('Chart.js备用CDN也加载失败');
            };
            document.head.appendChild(script);
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Noto+Sans+SC:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" onerror="console.warn('Google Fonts 加载失败')">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" onerror="console.warn('Font Awesome 加载失败')">
    <style>
        :root {
            /* 新的颜色系统 */
            --primary-100: #8FBF9F;
            --primary-200: #68a67d;
            --primary-300: #24613b;
            --accent-100: #F18F01;
            --accent-200: #833500;
            --text-100: #353535;
            --text-200: #5f5f5f;
            --bg-100: #F5ECD7;
            --bg-200: #ebe2cd;
            --bg-300: #c2baa6;

            /* 兼容性映射 */
            --bg-main: var(--bg-100);
            --text-primary: var(--text-100);
            --text-secondary: var(--text-200);
            --text-muted: var(--text-200);
            --primary: var(--primary-200);
            --primary-light: var(--primary-100);
            --accent: var(--accent-100);
            --accent-light: var(--accent-100);
            --border: var(--bg-300);

            /* 阴影保持不变 */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

            /* 更新渐变 */
            --gradient-primary: linear-gradient(135deg, var(--primary-200) 0%, var(--primary-100) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-100) 0%, var(--accent-200) 100%);
        }

        html { scroll-behavior: smooth; }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, var(--bg-100) 0%, var(--bg-200) 50%, var(--bg-300) 100%);
            color: var(--text-primary);
            line-height: 1.7;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(104, 166, 125, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(241, 143, 1, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(143, 191, 159, 0.03) 0%, transparent 50%);
            background-attachment: fixed;
            transition: background 0.5s ease, color 0.3s ease;
        }

        /* 平滑过渡动画 */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }

        .glass-container,
        .nav-menu,
        .timeline-item,
        .project-card,
        .glass-metric-card {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        strong {
            font-weight: 700;
            color: var(--primary);
        }

        .header-gradient strong {
            color: white;
            font-weight: 800;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .job-details p {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        /* Section Title Styling */
        .section-title {
            font-weight: 800;
            font-size: 3rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            padding-bottom: 1.5rem;
            margin-bottom: 4rem;
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            letter-spacing: -0.025em;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 4px;
            background: var(--gradient-accent);
            border-radius: 2px;
            animation: slideIn 0.8s ease-out forwards;
        }

        .section-icon {
            font-size: 2.5rem;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Animations */
        @keyframes slideIn { from { width: 0; } to { width: 80px; } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(30px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes glassShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
        }

        /* Glass Container Effect */
        .glass-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .glass-container::after {
            content: '';
            position: absolute;
            top: -50%; left: -50%;
            width: 200%; height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
            opacity: 0;
        }
        .glass-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 255, 255, 0.25);
        }
        .glass-container:hover::after {
            opacity: 1;
            animation: glassShine 1.2s ease-in-out;
        }

        /* Header Specific Styles */
        .header-gradient {
            background: linear-gradient(135deg, var(--primary-300) 0%, var(--primary-200) 100%);
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .particles-bg {
            position: absolute;
            inset: 0;
            background-image: radial-gradient(circle, rgba(255,255,255,0.05) 1px, transparent 1px);
            background-size: 20px 20px;
            animation: particleMove 20s linear infinite;
        }
        @keyframes particleMove { from { background-position: 0 0; } to { background-position: -400px -400px; } }
        .floating-circle {
            position: absolute;
            border-radius: 50%;
            animation-timing-function: ease-in-out;
            animation-iteration-count: infinite;
            animation-direction: alternate;
        }
        .animate-float-1 { top: 15%; left: 10%; animation: float1 6s infinite; }
        .animate-float-2 { top: 50%; right: 15%; animation: float2 8s infinite; }
        .animate-float-3 { bottom: 20%; left: 20%; animation: float3 7s infinite; }
        @keyframes float1 { 0%, 100% { transform: translate(0, 0) rotate(0deg); } 50% { transform: translate(30px, -20px) rotate(180deg); } }
        @keyframes float2 { 0%, 100% { transform: translate(0, 0) rotate(0deg); } 50% { transform: translate(-20px, 30px) rotate(-180deg); } }
        @keyframes float3 { 0%, 100% { transform: translate(0, 0) rotate(0deg); } 50% { transform: translate(25px, -15px) rotate(90deg); } }

        .header-title-animation { animation: fadeInUp 1.2s ease-out; }
        .subtitle-animation { animation: fadeInUp 1s ease-out 1s both; }
        .typing-animation {
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            border-right: 3px solid var(--accent);
            animation: typing 2s steps(4, end) 0.5s forwards, blink-caret .75s step-end infinite;
        }
        @keyframes typing { from { width: 0 } to { width: 100% } }
        @keyframes blink-caret { from, to { border-color: transparent } 50% { border-color: var(--accent); } }

        .glow-effect { animation: glow 2s ease-in-out infinite alternate; }
        @keyframes glow { from { box-shadow: 0 0 5px rgba(245, 158, 11, 0.5); } to { box-shadow: 0 0 20px rgba(245, 158, 11, 0.8), 0 0 30px rgba(245, 158, 11, 0.6); } }

        .contact-cards-animation { animation: fadeInUp 1s ease-out 1.5s both; }
        .contact-card {
            background: rgba(255, 255, 255, 0.12);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: 24px;
            padding: 1.75rem;
            display: flex; align-items: center; gap: 1.25rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative; overflow: hidden; cursor: pointer;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 2px 8px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }
        .contact-card:hover {
            transform: translateY(-12px) scale(1.03);
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(245, 158, 11, 0.6);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 10px 20px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.25);
        }
        .contact-icon-wrapper {
            width: 4rem; height: 4rem;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.9), rgba(245, 158, 11, 0.7));
            border-radius: 50%; display: flex; align-items: center; justify-content: center;
            box-shadow:
                0 8px 20px rgba(245, 158, 11, 0.4),
                0 4px 8px rgba(245, 158, 11, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }
        .contact-icon-wrapper::before {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.3), rgba(245, 158, 11, 0.1));
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .contact-card:hover .contact-icon-wrapper {
            transform: rotate(15deg) scale(1.15);
            box-shadow:
                0 15px 30px rgba(245, 158, 11, 0.6),
                0 8px 16px rgba(245, 158, 11, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        .contact-card:hover .contact-icon-wrapper::before { opacity: 1; }
        .contact-icon { font-size: 1.4rem; color: white; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); }
        .contact-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
        }
        .contact-value {
            font-size: 1rem;
            color: white;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            line-height: 1.2;
        }
        .card-glow { position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent); transition: left 0.6s; }
        .contact-card:hover .card-glow { left: 100%; }

        .job-intention-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
            backdrop-filter: blur(35px);
            border: 2px solid rgba(245, 158, 11, 0.4);
            border-radius: 32px;
            padding: 2.5rem 3.5rem;
            display: inline-flex;
            align-items: center;
            gap: 2rem;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 1s ease-out 2s both;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.15),
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        .job-intention-card:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: rgba(245, 158, 11, 0.7);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.25),
                0 12px 24px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        .intention-icon {
            width: 5rem; height: 5rem;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow:
                0 12px 24px rgba(245, 158, 11, 0.5),
                0 6px 12px rgba(245, 158, 11, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            animation: pulse 3s ease-in-out infinite;
            position: relative;
        }
        .intention-icon::before {
            content: '';
            position: absolute;
            inset: -3px;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.3), rgba(245, 158, 11, 0.1));
            border-radius: 50%;
            z-index: -1;
            animation: pulseRing 3s ease-in-out infinite;
        }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.08); } }
        @keyframes pulseRing { 0%, 100% { transform: scale(1); opacity: 0.7; } 50% { transform: scale(1.2); opacity: 0.3; } }
        .intention-icon i { font-size: 1.75rem; color: white; text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); }
        .intention-title {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.75rem;
        }
        .intention-item {
            display: flex;
            align-items: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        .intention-divider {
            color: rgba(245, 158, 11, 0.9);
            font-weight: bold;
            font-size: 1.3rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Navigation - 重新设计 */
        nav.sticky {
            position: sticky;
            top: 0;
            z-index: 50;
            backdrop-filter: blur(25px);
            background: rgba(255, 255, 255, 0.85);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid transparent;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        nav.scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border-bottom-color: var(--border);
        }

        .nav-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1rem 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .nav-menu {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 0.5rem;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            gap: 0.25rem;
        }

        .nav-link {
            position: relative;
            padding: 1rem 1.75rem;
            border-radius: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
            background: transparent;
            border: none;
            cursor: pointer;
        }

        .nav-link i {
            font-size: 1.1rem;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary);
            background: rgba(104, 166, 125, 0.08);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(104, 166, 125, 0.15);
        }

        .nav-link:hover i {
            opacity: 1;
            transform: scale(1.1);
        }

        .nav-link.active {
            color: white;
            font-weight: 700;
            background: var(--gradient-primary);
            box-shadow:
                0 6px 20px rgba(104, 166, 125, 0.3),
                0 2px 8px rgba(104, 166, 125, 0.2);
            transform: translateY(-1px);
        }

        .nav-link.active i {
            opacity: 1;
            color: white;
        }

        /* 导航进度指示器 */
        .nav-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: var(--gradient-primary);
            transition: all 0.3s ease;
            border-radius: 2px 2px 0 0;
        }

        /* 导航工具提示增强 */
        .nav-link::after {
            content: attr(data-section);
            position: absolute;
            bottom: -2.5rem;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .nav-link:hover::after {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .nav-link::after { display: none; }
        }

        /* KPI Cards - 增强版 */
        .glass-metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        .glass-metric-card:hover {
            background: rgba(255, 255, 255, 0.18) !important;
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-8px) scale(1.03);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
        }
        .glass-metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }
        .glass-metric-card:hover::before {
            left: 100%;
        }
        .number-counter { transition: all 0.3s ease; cursor: pointer; }
        .number-counter:hover { transform: scale(1.1); text-shadow: 0 0 15px rgba(104, 166, 125, 0.5); }
        .progress-bar { position: relative; background: rgba(104, 166, 125, 0.1); border-radius: 10px; overflow: hidden; height: 6px; margin-top: 0.5rem; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, var(--primary), var(--accent)); border-radius: 10px; width: 0%; transition: width 2s ease-out; }
        .progress-fill::after { content: ''; position: absolute; inset: 0; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent); animation: progressShine 2s ease-in-out infinite; }
        @keyframes progressShine { from { transform: translateX(-100%); } to { transform: translateX(100%); } }

        /* Timeline */
        .timeline-item { border-left: 4px solid rgba(104, 166, 125, 0.3); transition: all 0.4s ease; padding: 1.5rem 1.5rem 1.5rem 2.5rem; position: relative; background: rgba(255,255,255,0.08); backdrop-filter: blur(15px); border-radius: 0 20px 20px 0; margin-bottom: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.1); border-right: 1px solid rgba(255, 255, 255, 0.1); border-bottom: 1px solid rgba(255, 255, 255, 0.1); overflow: hidden; }
        .timeline-item:hover { border-left-color: var(--primary); background: rgba(255, 255, 255, 0.12); transform: translateX(12px); box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }
        .timeline-dot {
            position: absolute;
            left: -20px;
            top: 18px;
            height: 36px;
            width: 36px;
            border-radius: 50%;
            background: var(--gradient-primary);
            border: 3px solid var(--bg-main);
            transition: all 0.4s ease;
            box-shadow: 0 0 0 4px rgba(104, 166, 125, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .timeline-dot .icon-text {
            font-size: 18px;
            color: white !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            line-height: 1;
            font-weight: 900 !important;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            text-align: center;
        }

        /* 确保图标容器有足够的空间 */
        .timeline-dot {
            overflow: visible !important;
        }
        .timeline-item:hover .timeline-dot { transform: scale(1.2); box-shadow: 0 0 0 8px rgba(104, 166, 125, 0.3); }

        /* Project Cards - 增强版 */
        .project-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: var(--shadow);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            position: relative;
        }
        .project-card:hover {
            transform: translateY(-8px) scale(1.01);
            box-shadow: var(--shadow-lg);
            border-color: rgba(241, 143, 1, 0.3);
            background: rgba(255, 255, 255, 0.12);
        }
        .project-card-detail {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .project-tag {
            background: var(--gradient-accent);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }
        .project-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }
        .project-tag:hover::before {
            left: 100%;
        }

        /* 项目时间线 */
        .project-timeline {
            position: relative;
            padding: 1rem 0;
            margin: 1rem 0;
        }
        .project-timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--primary), var(--accent));
        }
        .timeline-point {
            position: relative;
            padding-left: 3rem;
            margin-bottom: 1rem;
        }
        .timeline-point::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            background: var(--primary);
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 3px rgba(104, 166, 125, 0.2);
        }

        /* 项目影响力雷达 */
        .impact-radar {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }
        .radar-bg {
            position: absolute;
            inset: 0;
            border-radius: 50%;
            border: 2px solid rgba(104, 166, 125, 0.2);
        }
        .radar-fill {
            position: absolute;
            inset: 0;
            border-radius: 50%;
            background: conic-gradient(from 0deg, var(--primary) 0deg, var(--accent) 120deg, var(--primary) 240deg, var(--accent) 360deg);
            opacity: 0.3;
            animation: radarSpin 4s linear infinite;
        }
        @keyframes radarSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .filter-btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            border: 2px solid var(--border);
            background: white;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .filter-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(104, 166, 125, 0.1), transparent);
            transition: left 0.6s;
        }
        .filter-btn:hover {
            color: var(--primary);
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        .filter-btn:hover::before {
            left: 100%;
        }
        .filter-btn.active {
            background: var(--gradient-primary);
            color: white;
            border-color: var(--primary);
        }

        /* General & Utility */
        .fade-in-section { opacity: 0; transform: translateY(40px); transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1); }
        .fade-in-section.is-visible { opacity: 1; transform: translateY(0); }
        .stagger-animation > * { opacity: 0; transform: translateY(20px); transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); }
        .fade-in-section.is-visible .stagger-animation > * { opacity: 1; transform: translateY(0); }
        .stagger-animation > *:nth-child(1) { transition-delay: 0.1s; } .stagger-animation > *:nth-child(2) { transition-delay: 0.2s; }
        .stagger-animation > *:nth-child(3) { transition-delay: 0.3s; } .stagger-animation > *:nth-child(4) { transition-delay: 0.4s; }
        .stagger-animation > *:nth-child(5) { transition-delay: 0.5s; } .stagger-animation > *:nth-child(6) { transition-delay: 0.6s; }

        /* 增强视觉层次感 */
        .section-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--border), transparent);
            margin: 4rem 0;
            position: relative;
        }
        .section-divider::after {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 5px;
            background: var(--gradient-accent);
            border-radius: 3px;
        }

        /* 优化卡片阴影层次 */
        .enhanced-shadow {
            box-shadow:
                0 1px 3px rgba(0, 0, 0, 0.12),
                0 1px 2px rgba(0, 0, 0, 0.24),
                0 0 0 1px rgba(255, 255, 255, 0.05) inset;
        }
        .enhanced-shadow:hover {
            box-shadow:
                0 14px 28px rgba(0, 0, 0, 0.25),
                0 10px 10px rgba(0, 0, 0, 0.22),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        }

        /* 浮动操作按钮组 */
        .floating-actions {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 100;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .floating-btn {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transform: translateY(20px);
        }

        .floating-btn.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .floating-btn:hover {
            transform: translateY(-4px) scale(1.1);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
        }

        .back-to-top {
            background: var(--gradient-primary);
            color: white;
        }



        .contact-btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .share-btn {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-300) 0%, var(--primary-200) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: help;
        }
        .tooltip::after {
            content: attr(data-tooltip);
            position: fixed;
            bottom: auto;
            top: auto;
            left: 50%;
            transform: translateX(-50%) translateY(-100%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            max-width: 300px;
            white-space: normal;
            text-align: center;
            line-height: 1.4;
        }
        .tooltip::before {
            content: '';
            position: fixed;
            bottom: auto;
            top: auto;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            border: 6px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10000;
        }
        .tooltip:hover::after,
        .tooltip:hover::before {
            opacity: 1;
        }

        /* 更稳定的tooltip实现 */
        .tooltip-stable {
            position: relative;
            cursor: help;
        }
        .tooltip-stable::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: calc(100% + 10px);
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            max-width: 280px;
            white-space: normal;
            text-align: center;
            line-height: 1.4;
            min-width: 200px;
        }
        .tooltip-stable::before {
            content: '';
            position: absolute;
            bottom: calc(100% + 4px);
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10000;
        }
        .tooltip-stable:hover::after,
        .tooltip-stable:hover::before {
            opacity: 1;
        }

        .certification-badge { background: linear-gradient(135deg, var(--primary-300) 0%, var(--primary-200) 100%); color: white; padding: 0.5rem 1rem; border-radius: 25px; font-size: 0.875rem; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem; margin: 0.25rem; box-shadow: var(--shadow-md); transition: all 0.3s ease; position: relative; overflow: hidden; }
        .certification-badge:hover { transform: translateY(-2px) scale(1.05); box-shadow: var(--shadow-lg); }

        /* 响应式设计优化 */
        @media (max-width: 1024px) {
            .section-title { font-size: 2.5rem; }
            .glass-container { padding: 1.5rem; }
            .timeline-item { padding: 1rem 1rem 1rem 2rem; }

            .nav-container { padding: 0.75rem 1rem; }
            .nav-menu { padding: 0.4rem; }
            .nav-link {
                padding: 0.875rem 1.5rem;
                font-size: 0.95rem;
            }
            .nav-link i { font-size: 1rem; }
        }

        @media (max-width: 768px) {
            .section-title { font-size: 2.2rem; margin-bottom: 2rem; }

            .nav-container { padding: 0.5rem; }
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
                padding: 0.3rem;
                gap: 0.2rem;
            }
            .nav-link {
                padding: 0.75rem 1.25rem;
                font-size: 0.9rem;
                border-radius: 12px;
            }
            .nav-link span { display: none; }
            .nav-link i {
                font-size: 1.2rem;
                margin: 0;
            }
            .nav-link { min-width: 3rem; justify-content: center; }
            .job-intention-card {
                padding: 2rem 1.5rem;
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
                border-radius: 24px;
            }
            .intention-details { justify-content: center; }
            .intention-divider { display: none; }
            .intention-item { margin: 0.5rem 0; font-size: 1.1rem; }
            .intention-icon { width: 4rem; height: 4rem; }
            .intention-title { font-size: 1rem; margin-bottom: 1rem; }
            .glass-container { padding: 1rem; margin-bottom: 1.5rem; }
            .contact-card {
                padding: 1.25rem;
                gap: 1rem;
                border-radius: 20px;
            }
            .contact-icon-wrapper { width: 3rem; height: 3rem; }
            .contact-icon { font-size: 1.2rem; }
            .contact-value { font-size: 0.9rem; }
            .contact-label { font-size: 0.75rem; }
            .timeline-item { margin-bottom: 1.5rem; padding: 1rem 1rem 1rem 1.5rem; }
            .timeline-dot { left: -14px; width: 28px; height: 28px; border: 2px solid var(--bg-main); }
            .timeline-dot .icon-text { font-size: 14px; }
        }

        @media (max-width: 480px) {
            .section-title { font-size: 1.8rem; }
            .header-title-animation h1 { font-size: 3.5rem; }
            .contact-cards-animation { grid-template-columns: 1fr; gap: 1rem; }
            .contact-card { padding: 1rem; }
            .contact-icon-wrapper { width: 2.5rem; height: 2.5rem; }
            .contact-icon { font-size: 1rem; }
            .job-intention-card {
                padding: 1.5rem;
                margin: 0 1rem;
                border-radius: 20px;
            }
            .intention-icon { width: 3.5rem; height: 3.5rem; }
            .intention-icon i { font-size: 1.5rem; }
            .glass-metric-card { padding: 0.75rem; }
            .project-card { margin-bottom: 1rem; }
            .floating-actions {
                bottom: 1rem;
                right: 1rem;
                gap: 0.75rem;
            }

            .floating-btn {
                width: 3rem;
                height: 3rem;
                font-size: 1rem;
            }

            .nav-container { padding: 0.4rem; }
            .nav-menu { padding: 0.25rem; }
            .nav-link {
                padding: 0.6rem 1rem;
                min-width: 2.8rem;
            }
            .nav-link i { font-size: 1.1rem; }
        }

        /* 阅读进度条 - 新增 */
        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            z-index: 1000;
            transition: width 0.1s ease;
            box-shadow: 0 2px 10px rgba(104, 166, 125, 0.3);
        }

        /* 视差滚动效果 - 新增 */
        .parallax-bg {
            transform: translateZ(0);
            will-change: transform;
        }

        /* 成就徽章系统 - 新增 */
        .achievement-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, var(--accent-100), var(--accent-200));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(241, 143, 1, 0.3);
            animation: achievementPulse 2s ease-in-out infinite;
        }

        @keyframes achievementPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 数据对比可视化 - 新增 */
        .comparison-bar {
            position: relative;
            height: 8px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        .comparison-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            border-radius: 4px;
            transition: width 1.5s ease-out;
            position: relative;
        }
        .comparison-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s ease-in-out infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 环形进度图 - 新增 */
        .circular-progress {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto;
        }
        .circular-progress svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }
        .circular-progress .bg-circle {
            fill: none;
            stroke: rgba(0, 0, 0, 0.1);
            stroke-width: 8;
        }
        .circular-progress .progress-circle {
            fill: none;
            stroke: url(#gradient);
            stroke-width: 8;
            stroke-linecap: round;
            stroke-dasharray: 251.2;
            stroke-dashoffset: 251.2;
            transition: stroke-dashoffset 2s ease-in-out;
        }
        .circular-progress .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            font-weight: bold;
            color: var(--primary);
        }

        /* 动态柱状图 */
        .bar-chart {
            display: flex;
            align-items: end;
            height: 100px;
            gap: 8px;
            padding: 10px;
        }
        .bar {
            flex: 1;
            background: linear-gradient(to top, var(--primary), var(--accent));
            border-radius: 4px 4px 0 0;
            min-height: 10px;
            transition: height 1.5s ease-out;
            position: relative;
        }
        .bar::after {
            content: attr(data-value);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            font-weight: bold;
            color: var(--text-primary);
        }

        /* 技能点系统 */
        .skill-point {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin: 2px;
            transition: all 0.3s ease;
        }
        .skill-point.filled {
            background: var(--primary);
            box-shadow: 0 0 8px rgba(104, 166, 125, 0.5);
        }
        .skill-point.empty {
            background: rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(104, 166, 125, 0.3);
        }
        .skill-point:hover {
            transform: scale(1.2);
        }

        /* 快速浏览模式 - 新增 */
        .quick-view-mode .glass-container,
        .quick-view-mode .project-card,
        .quick-view-mode .timeline-item {
            padding: 1rem !important;
        }

        .quick-view-mode .section-title {
            font-size: 2rem !important;
            margin-bottom: 2rem !important;
        }

        .quick-view-mode .job-details,
        .quick-view-mode .project-card-detail {
            display: none !important;
        }

        .quick-view-mode .glass-metric-card {
            padding: 0.75rem !important;
        }

        .quick-view-mode .glass-metric-card .text-4xl {
            font-size: 2rem !important;
        }

        .quick-view-mode .contact-card {
            padding: 1rem !important;
        }

        /* 性能优化样式 - 新增 */
        .lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }
        .lazy.loaded {
            opacity: 1;
        }

        /* 减少动画模式 */
        .reduce-motion * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        /* GPU加速优化 */
        .glass-container,
        .project-card,
        .timeline-item,
        .contact-card {
            will-change: transform;
            transform: translateZ(0);
        }

        /* 内容优先加载 */
        .content-priority {
            content-visibility: auto;
            contain-intrinsic-size: 300px;
        }

        /* 移除暗色主题，专注亮色模式体验 */

        /* 打印样式优化 */
        @media print {
            .loading-overlay,
            .floating-actions,
            nav,
            .particles-bg,
            .floating-circle,
            footer {
                display: none !important;
            }

            body {
                background: white !important;
                color: black !important;
                font-size: 12pt;
                line-height: 1.4;
            }

            .glass-container,
            .project-card,
            .timeline-item {
                background: white !important;
                border: 1px solid #ccc !important;
                box-shadow: none !important;
                backdrop-filter: none !important;
                page-break-inside: avoid;
            }

            .section-title {
                color: #333 !important;
                font-size: 18pt !important;
                page-break-after: avoid;
            }

            .header-gradient {
                background: #333 !important;
                color: white !important;
            }
        }

        /* 浏览器兼容性支持 */
        .glass-container {
            /* 为不支持backdrop-filter的浏览器提供备用样式 */
            background: rgba(255, 255, 255, 0.9);
        }

        @supports (backdrop-filter: blur(10px)) {
            .glass-container {
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                background: rgba(255, 255, 255, 0.8);
            }
        }

        /* 错误状态样式 */
        .error-fallback {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: #6b7280;
            background: #f9fafb;
            border-radius: 0.5rem;
            border: 1px dashed #d1d5db;
        }

        .error-fallback i {
            font-size: 2rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 减少动画效果（无障碍支持） */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body class="antialiased">
    <!-- 阅读进度条 -->
    <div class="reading-progress" id="reading-progress"></div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- ========== Header Section ========== -->
    <header class="header-gradient">
        <div class="particles-bg"></div>
        <div class="absolute inset-0">
            <div class="floating-circle absolute w-20 h-20 bg-white/10 rounded-full blur-sm animate-float-1"></div>
            <div class="floating-circle absolute w-32 h-32 bg-white/5 rounded-full blur-md animate-float-2"></div>
            <div class="floating-circle absolute w-16 h-16 bg-white/15 rounded-full blur-sm animate-float-3"></div>
        </div>

        <div class="container mx-auto px-6 text-center relative z-10 py-20">
            <div class="mb-16 header-title-animation">
                <div class="relative inline-block mb-8">
                    <h1 class="text-7xl font-extrabold text-white mb-6 tracking-wide typing-animation">翟生</h1>
                    <div class="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-24 h-1.5 bg-gradient-to-r from-amber-400 to-amber-300 rounded-full glow-effect"></div>
                </div>
                <p class="text-2xl text-white/95 mt-8 mb-12 subtitle-animation font-medium leading-relaxed">
                    政企数字化销售专家 | AI场景化落地领军人
                </p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto mb-16 contact-cards-animation">
                <div class="contact-card group">
                    <div class="contact-icon-wrapper"><i class="fas fa-user contact-icon"></i></div>
                    <div class="flex-1 text-left"><div class="contact-label">基本信息</div><div class="contact-value">男 | 36岁</div></div>
                    <div class="card-glow"></div>
                </div>
                <div class="contact-card group">
                    <div class="contact-icon-wrapper"><i class="fas fa-briefcase contact-icon"></i></div>
                    <div class="flex-1 text-left"><div class="contact-label">工作经验</div><div class="contact-value"><strong>11</strong> 年</div></div>
                    <div class="card-glow"></div>
                </div>
                <div class="contact-card group">
                    <div class="contact-icon-wrapper"><i class="fas fa-envelope contact-icon"></i></div>
                    <div class="flex-1 text-left"><div class="contact-label">邮箱</div><div class="contact-value"><a href="mailto:<EMAIL>" class="hover:text-amber-300 transition-colors"><EMAIL></a></div></div>
                    <div class="card-glow"></div>
                </div>
                <div class="contact-card group">
                    <div class="contact-icon-wrapper"><i class="fas fa-phone contact-icon"></i></div>
                    <div class="flex-1 text-left"><div class="contact-label">电话</div><div class="contact-value">132 6033 5189</div></div>
                    <div class="card-glow"></div>
                </div>
            </div>

            <div class="job-intention-card">
                <div class="intention-icon"><i class="fas fa-bullseye"></i></div>
                <div class="text-left">
                    <div class="intention-title">求职意向</div>
                    <div class="flex items-center gap-4 flex-wrap">
                        <span class="intention-item"><i class="fas fa-crown mr-2 text-amber-300"></i>销售总监</span>
                        <span class="intention-divider hidden md:inline">|</span>
                        <span class="intention-item"><i class="fas fa-dollar-sign mr-2 text-amber-300"></i>15-25K</span>
                        <span class="intention-divider hidden md:inline">|</span>
                        <span class="intention-item"><i class="fas fa-location-dot mr-2 text-amber-300"></i>广州/深圳</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- ========== Navigation Bar ========== -->
    <nav id="navbar" class="sticky">
        <div class="nav-container">
            <div class="nav-menu">
                <a href="#competency" class="nav-link" data-section="展示核心竞争力与业绩亮点">
                    <i class="fas fa-star"></i>
                    <span>核心能力</span>
                </a>
                <a href="#career" class="nav-link" data-section="详细职业发展历程">
                    <i class="fas fa-route"></i>
                    <span>职业生涯</span>
                </a>
                <a href="#projects" class="nav-link" data-section="重点项目案例分析">
                    <i class="fas fa-rocket"></i>
                    <span>项目亮点</span>
                </a>
                <a href="#education" class="nav-link" data-section="教育背景与专业认证">
                    <i class="fas fa-graduation-cap"></i>
                    <span>教育背景</span>
                </a>
                <div class="nav-progress" id="nav-progress"></div>
            </div>
        </div>
    </nav>

    <main class="container mx-auto px-8 py-24">

        <!-- ========== Core Competency Section ========== -->
        <section id="competency" class="mb-40 scroll-mt-24 fade-in-section">
            <h2 class="section-title"><i class="section-icon fas fa-star"></i>核心能力</h2>
            <p class="max-w-5xl mx-auto text-center mb-20 text-xl font-medium leading-relaxed text-gray-600">
                深耕数字教育与新能源领域<strong>11年</strong>，服务海信、科大讯飞、三一等<strong>3家百亿级企业</strong>，专注将<strong>AIoT技术</strong>转化为可量化的商业价值，擅长构建“<strong>技术研发-场景落地-持续运营</strong>”的全周期价值交付体系。
            </p>

            <!-- 核心价值主张增强 - 新增 -->
            <div class="max-w-7xl mx-auto mb-20">
                <div class="text-center mb-16">
                    <div class="inline-flex items-center gap-4 rounded-full px-8 py-4 mb-6 border shadow-sm" style="background: linear-gradient(to right, rgba(143, 191, 159, 0.1), rgba(241, 143, 1, 0.1)); border-color: rgba(143, 191, 159, 0.3);">
                        <div class="w-3 h-3 rounded-full animate-pulse" style="background-color: var(--primary-200);"></div>
                        <span class="text-lg font-bold" style="color: var(--primary-300);">核心价值主张</span>
                        <div class="w-3 h-3 rounded-full animate-pulse" style="background-color: var(--accent-100);"></div>
                    </div>
                    <p class="text-2xl font-bold mb-4 leading-relaxed" style="color: var(--text-100);">
                        将<span class="px-3 py-1 rounded-lg mx-1" style="color: var(--primary-300); background-color: rgba(143, 191, 159, 0.1);">AIoT技术</span>转化为
                        <span class="px-3 py-1 rounded-lg mx-1" style="color: var(--accent-200); background-color: rgba(241, 143, 1, 0.1);">可量化商业价值</span>的
                        <span class="px-3 py-1 rounded-lg mx-1" style="color: var(--primary-200); background-color: rgba(104, 166, 125, 0.1);">数字化销售专家</span>
                    </p>
                </div>

                <!-- 一句话亮点展示 - 新增 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <div class="text-center p-8 rounded-2xl hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer" style="background: linear-gradient(to bottom right, rgba(143, 191, 159, 0.1), rgba(143, 191, 159, 0.2)); border: 1px solid rgba(143, 191, 159, 0.3);">
                        <div class="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg" style="background: linear-gradient(to bottom right, var(--primary-200), var(--primary-300));">
                            <i class="fas fa-trophy text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold mb-3 text-xl" style="color: var(--primary-300);">业绩突破</h3>
                        <p class="text-base leading-relaxed" style="color: var(--primary-200);">单项目最高中标<strong style="color: var(--primary-300);">1350万</strong>，4年斩获<strong style="color: var(--primary-300);">11个</strong>千万级项目</p>
                    </div>
                    <div class="text-center p-8 rounded-2xl hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer" style="background: linear-gradient(to bottom right, rgba(241, 143, 1, 0.1), rgba(241, 143, 1, 0.2)); border: 1px solid rgba(241, 143, 1, 0.3);">
                        <div class="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg" style="background: linear-gradient(to bottom right, var(--accent-100), var(--accent-200));">
                            <i class="fas fa-users text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold mb-3 text-xl" style="color: var(--accent-200);">团队领导</h3>
                        <p class="text-base leading-relaxed" style="color: var(--accent-100);">管理<strong style="color: var(--accent-200);">30+人</strong>矩阵团队，3年输送<strong style="color: var(--accent-200);">3名</strong>区域总监</p>
                    </div>
                    <div class="text-center p-8 rounded-2xl hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer" style="background: linear-gradient(to bottom right, rgba(104, 166, 125, 0.1), rgba(104, 166, 125, 0.2)); border: 1px solid rgba(104, 166, 125, 0.3);">
                        <div class="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg" style="background: linear-gradient(to bottom right, var(--primary-100), var(--primary-200));">
                            <i class="fas fa-chart-line text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold mb-3 text-xl" style="color: var(--primary-200);">客户价值</h3>
                        <p class="text-base leading-relaxed" style="color: var(--primary-100);">客户续约率<strong style="color: var(--primary-200);">95%</strong>，3年LTV提升<strong style="color: var(--primary-200);">280%</strong></p>
                    </div>
                </div>
            </div>

            <div class="glass-container rounded-2xl p-10 mb-20 relative overflow-hidden">
                <!-- 背景装饰 -->
                <div class="absolute top-0 right-0 w-32 h-32 rounded-full opacity-20 -translate-y-16 translate-x-16" style="background: linear-gradient(to bottom right, rgba(143, 191, 159, 0.3), rgba(241, 143, 1, 0.3));"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 rounded-full opacity-20 translate-y-12 -translate-x-12" style="background: linear-gradient(to top right, rgba(241, 143, 1, 0.3), rgba(143, 191, 159, 0.3));"></div>

                <div class="relative z-10">
                    <div class="text-center mb-12">
                        <div class="inline-flex items-center gap-3 rounded-full px-6 py-3 mb-4 border" style="background: linear-gradient(to right, rgba(241, 143, 1, 0.1), rgba(241, 143, 1, 0.2)); border-color: rgba(241, 143, 1, 0.3);">
                            <i class="fas fa-medal text-xl" style="color: var(--accent-100);"></i>
                            <span class="text-xl font-bold" style="color: var(--accent-200);">标杆业绩亮点</span>
                            <i class="fas fa-star" style="color: var(--accent-100);"></i>
                        </div>
                        <p class="text-sm max-w-2xl mx-auto" style="color: var(--text-200);">以下数据均为可验证的真实业绩，体现了在政企数字化销售领域的卓越表现</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 stagger-animation">
                    <!-- 增强版业绩卡片 -->
                    <div class="text-center glass-metric-card rounded-xl p-8 enhanced-shadow relative group" style="overflow: visible;">
                        <div class="absolute top-0 right-0 w-16 h-16 rounded-full opacity-20 -translate-y-8 translate-x-8" style="background-color: rgba(143, 191, 159, 0.3);"></div>
                        <div class="relative z-10">
                            <div class="flex items-center justify-center mb-4">
                                <div class="w-14 h-14 rounded-full flex items-center justify-center mr-4" style="background: linear-gradient(to bottom right, var(--primary-200), var(--primary-300));">
                                    <i class="fas fa-trophy text-white text-xl"></i>
                                </div>
                                <div class="text-left">
                                    <div class="text-4xl font-bold number-counter tooltip-stable" data-target="1350" data-suffix="万" data-tooltip="辽宁省教育云平台项目 - 行业平均500万" style="color: var(--primary-200);">0</div>
                                    <div class="text-sm font-medium mt-1" style="color: var(--primary-100);">vs 行业平均 500万</div>
                                </div>
                            </div>
                            <p class="font-medium mb-3 text-lg" style="color: var(--text-100);">单项目最高中标额</p>
                            <div class="progress-bar"><div class="progress-fill" data-progress="100"></div></div>
                            <div class="text-sm font-semibold mt-2" style="color: var(--accent-100);">超越行业平均 170%</div>
                        </div>
                    </div>

                    <div class="text-center glass-metric-card rounded-xl p-6 enhanced-shadow relative group" style="overflow: visible;">
                        <div class="absolute top-0 right-0 w-16 h-16 rounded-full opacity-20 -translate-y-8 translate-x-8" style="background-color: rgba(241, 143, 1, 0.3);"></div>
                        <div class="relative z-10">
                            <div class="flex items-center justify-center mb-3">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center mr-3" style="background: linear-gradient(to bottom right, var(--accent-100), var(--accent-200));">
                                    <i class="fas fa-map-marked-alt text-white text-lg"></i>
                                </div>
                                <div class="text-left">
                                    <div class="text-4xl font-bold tooltip-stable" data-tooltip="覆盖辽宁、河南、山西、内蒙古等省份 - 行业平均2省" style="color: var(--accent-100);">4省8市</div>
                                    <div class="text-xs font-medium" style="color: var(--accent-200);">vs 行业平均 2省</div>
                                </div>
                            </div>
                            <p class="font-medium mb-2" style="color: var(--text-100);">教育信息化政企合作</p>
                            <div class="progress-bar"><div class="progress-fill" data-progress="80"></div></div>
                            <div class="text-xs font-semibold mt-1" style="color: var(--accent-100);">覆盖范围领先 100%</div>
                        </div>
                    </div>

                    <div class="text-center glass-metric-card rounded-xl p-6 enhanced-shadow relative group" style="overflow: visible;">
                        <div class="absolute top-0 right-0 w-16 h-16 rounded-full opacity-20 -translate-y-8 translate-x-8" style="background-color: rgba(104, 166, 125, 0.3);"></div>
                        <div class="relative z-10">
                            <div class="flex items-center justify-center mb-3">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center mr-3" style="background: linear-gradient(to bottom right, var(--primary-100), var(--primary-200));">
                                    <i class="fas fa-handshake text-white text-lg"></i>
                                </div>
                                <div class="text-left">
                                    <div class="text-4xl font-bold number-counter tooltip-stable" data-target="95" data-suffix="%" data-tooltip="基于长期合作关系维护 - 行业平均65%" style="color: var(--primary-200);">0</div>
                                    <div class="text-xs font-medium" style="color: var(--primary-100);">vs 行业平均 65%</div>
                                </div>
                            </div>
                            <p class="font-medium mb-2" style="color: var(--text-100);">客户续约率 (3年)</p>
                            <div class="progress-bar"><div class="progress-fill" data-progress="95"></div></div>
                            <div class="text-xs font-semibold mt-1" style="color: var(--accent-100);">高出行业标准 46%</div>
                        </div>
                    </div>

                    <div class="text-center glass-metric-card rounded-xl p-6 enhanced-shadow relative group" style="overflow: visible;">
                        <div class="absolute top-0 right-0 w-16 h-16 rounded-full opacity-20 -translate-y-8 translate-x-8" style="background-color: rgba(241, 143, 1, 0.3);"></div>
                        <div class="relative z-10">
                            <div class="flex items-center justify-center mb-3">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center mr-3" style="background: linear-gradient(to bottom right, var(--accent-100), var(--accent-200));">
                                    <i class="fas fa-chart-line text-white text-lg"></i>
                                </div>
                                <div class="text-left">
                                    <div class="text-4xl font-bold number-counter tooltip" data-target="19.7" data-suffix="%" data-decimal="1" data-tooltip="山西华能汾阳100MW项目 - 行业平均12%" style="color: var(--accent-100);">0</div>
                                    <div class="text-xs font-medium" style="color: var(--accent-200);">vs 行业平均 12%</div>
                                </div>
                            </div>
                            <p class="font-medium mb-2" style="color: var(--text-100);">风电项目IRR提升</p>
                            <div class="progress-bar"><div class="progress-fill" data-progress="85"></div></div>
                            <div class="text-xs font-semibold mt-1" style="color: var(--accent-100);">投资回报率 +64%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔间距 -->
            <div class="mb-16"></div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-20 stagger-animation">
                <div class="text-center glass-metric-card rounded-xl p-6"><div class="text-3xl font-bold number-counter mb-2" data-target="30" data-suffix="+" style="color: var(--primary-200);">0</div><p class="text-base mt-2" style="color: var(--text-200);">矩阵团队管理</p></div>
                <div class="text-center glass-metric-card rounded-xl p-6"><div class="text-3xl font-bold number-counter mb-2" data-target="11" style="color: var(--primary-200);">0</div><p class="text-base mt-2" style="color: var(--text-200);">千万级项目</p></div>
                <div class="text-center glass-metric-card rounded-xl p-6"><div class="text-3xl font-bold number-counter mb-2" data-target="32.8" data-decimal="1" style="color: var(--primary-200);">0</div><p class="text-base mt-2" style="color: var(--text-200);">主机销售带动(亿)</p></div>
                <div class="text-center glass-metric-card rounded-xl p-6"><div class="text-3xl font-bold number-counter mb-2" data-target="89" data-suffix="%" style="color: var(--primary-200);">0</div><p class="text-base mt-2" style="color: var(--text-200);">线索转化率</p></div>
                <div class="text-center glass-metric-card rounded-xl p-6"><div class="text-3xl font-bold number-counter mb-2" data-target="85" data-suffix="%" style="color: var(--primary-200);">0</div><p class="text-base mt-2" style="color: var(--text-200);">需求预测准确率</p></div>
                <div class="text-center glass-metric-card rounded-xl p-6"><div class="text-3xl font-bold number-counter mb-2" data-target="60" data-suffix="%" style="color: var(--primary-200);">0</div><p class="text-base mt-2" style="color: var(--text-200);">方案周期缩短</p></div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <!-- 技能雷达图 -->
                <div class="glass-container rounded-2xl p-10">
                    <h3 class="text-2xl font-bold mb-6 text-center" style="color: var(--primary-200);"><i class="fas fa-chart-radar mr-3"></i>技能雷达图</h3>
                    <div class="relative">
                        <canvas id="skillsRadarChart" width="300" height="300"></canvas>
                    </div>
                </div>

                <!-- 核心技能环形图 -->
                <div class="glass-container rounded-2xl p-10">
                    <h3 class="text-2xl font-bold mb-6 text-center" style="color: var(--primary-200);"><i class="fas fa-bullseye mr-3"></i>核心技能</h3>
                    <div class="space-y-6">
                        <div class="text-center">
                            <div class="circular-progress mb-2">
                                <svg>
                                    <defs>
                                        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                    <circle class="bg-circle" cx="40" cy="40" r="36"></circle>
                                    <circle class="progress-circle" cx="40" cy="40" r="36" style="stroke: url(#gradient1);" data-progress="95"></circle>
                                </svg>
                                <div class="progress-text">95%</div>
                            </div>
                            <p class="text-sm font-medium text-gray-700">销售管理</p>
                        </div>
                        <div class="text-center">
                            <div class="circular-progress mb-2">
                                <svg>
                                    <defs>
                                        <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                    <circle class="bg-circle" cx="40" cy="40" r="36"></circle>
                                    <circle class="progress-circle" cx="40" cy="40" r="36" style="stroke: url(#gradient2);" data-progress="92"></circle>
                                </svg>
                                <div class="progress-text">92%</div>
                            </div>
                            <p class="text-sm font-medium text-gray-700">团队领导</p>
                        </div>
                        <div class="text-center">
                            <div class="circular-progress mb-2">
                                <svg>
                                    <defs>
                                        <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                    <circle class="bg-circle" cx="40" cy="40" r="36"></circle>
                                    <circle class="progress-circle" cx="40" cy="40" r="36" style="stroke: url(#gradient3);" data-progress="96"></circle>
                                </svg>
                                <div class="progress-text">96%</div>
                            </div>
                            <p class="text-sm font-medium text-gray-700">客户关系</p>
                        </div>
                    </div>
                </div>

                <div class="glass-container rounded-2xl p-10">
                    <h3 class="text-2xl font-bold mb-6 text-center" style="color: var(--primary-200);"><i class="fas fa-cogs mr-3"></i>核心能力矩阵</h3>
                    <div class="space-y-4">
                        <div class="p-4 rounded-lg bg-blue-50 border-l-4 border-blue-500"><h4 class="font-semibold text-gray-800"><i class="fas fa-chess mr-2 text-blue-500"></i>战略级资源整合</h4><p class="text-gray-600 text-sm">主导<strong>4省8市</strong>政企合作，构建“政-校-企”协作模式；整合AI与硬件生态链，年采购超<strong>800万</strong>。</p></div>
                        <div class="p-4 rounded-lg bg-green-50 border-l-4 border-green-500"><h4 class="font-semibold text-gray-800"><i class="fas fa-bullseye mr-2 text-green-500"></i>战略能力显性化</h4><p class="text-gray-600 text-sm">主导<strong>5年BU规划</strong>，实现年复合增长<strong>28%</strong>；构建动态KPI机制，渠道贡献率提升<strong>45%</strong>。</p></div>
                        <div class="p-4 rounded-lg bg-purple-50 border-l-4 border-purple-500"><h4 class="font-semibold text-gray-800"><i class="fas fa-chart-line mr-2 text-purple-500"></i>数据智能驱动</h4><p class="text-gray-600 text-sm">构建需求预测模型，准确率<strong>85%</strong>；智能诊断系统使线索转化率从<strong>52%提升至89%</strong>。</p></div>
                        <div class="p-4 rounded-lg bg-amber-50 border-l-4 border-amber-500"><h4 class="font-semibold text-gray-800"><i class="fas fa-robot mr-2 text-amber-500"></i>AI工具应用创新</h4><p class="text-gray-600 text-sm">运用<strong>ChatGPT</strong>优化投标，周期缩短<strong>60%</strong>；部署<strong>Claude</strong>智能分析，需求挖掘准确率提升<strong>45%</strong>。</p></div>
                    </div>
                </div>
                 <div class="glass-container rounded-2xl p-10">
                    <h3 class="text-2xl font-bold mb-6 text-center" style="color: var(--primary-200);"><i class="fas fa-trophy mr-3"></i>行业突破性成就</h3>
                    <div class="space-y-4">
                        <div class="p-4 rounded-lg bg-blue-50 border-l-4 border-blue-500"><h4 class="font-semibold text-gray-800"><i class="fas fa-lightbulb mr-2 text-blue-600"></i>开创"AI+教育装备"新模式</h4><p class="text-gray-600 text-sm">斩获<strong>5所985高校</strong>合作，构建三级分销网，渠道覆盖率从<strong>32%跃升至62%</strong>。</p></div>
                        <div class="p-4 rounded-lg bg-green-50 border-l-4 border-green-500"><h4 class="font-semibold text-gray-800"><i class="fas fa-chart-pie mr-2 text-green-600"></i>数字化方案提升客户价值</h4><p class="text-gray-600 text-sm"><strong>3年LTV提升280%</strong>，续约率<strong>>95%</strong>；促成<strong>50MW风电项目IRR提升至19.7%</strong>。</p></div>
                        <div class="p-4 rounded-lg bg-purple-50 border-l-4 border-purple-500"><h4 class="font-semibold text-gray-800"><i class="fas fa-award mr-2 text-purple-600"></i>行业标杆与专业认证</h4><p class="text-gray-600 text-sm">主导方案入选《教育信息化白皮书》；<strong>4年斩获11个千万级项目</strong>，单项目最高<strong>1350万</strong>。</p></div>
                        <div class="p-4 rounded-lg bg-amber-50 border-l-4 border-amber-500"><h4 class="font-semibold text-gray-800"><i class="fas fa-users mr-2 text-orange-600"></i>团队培养与管理突破</h4><p class="text-gray-600 text-sm">带队<strong>30+人</strong>，搭建销售铁军培养体系，<strong>3年内输送3名区域总监</strong>。</p></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- ========== Career Path Section ========== -->
        <section id="career" class="mb-40 scroll-mt-24 fade-in-section">
            <h2 class="section-title"><i class="section-icon fas fa-route"></i>职业生涯</h2>
            <div class="relative" id="timeline-container">
                <!-- Career data will be injected here by JavaScript -->
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- ========== Project Highlights Section ========== -->
        <section id="projects" class="mb-40 scroll-mt-24 fade-in-section">
            <h2 class="section-title"><i class="section-icon fas fa-rocket"></i>项目亮点</h2>

            <!-- 项目统计概览 - 新增 -->
            <div class="max-w-5xl mx-auto mb-16">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center p-8 rounded-2xl border" style="background: linear-gradient(to bottom right, rgba(143, 191, 159, 0.1), rgba(143, 191, 159, 0.2)); border-color: rgba(143, 191, 159, 0.3);">
                        <div class="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6" style="background: linear-gradient(to bottom right, var(--primary-200), var(--primary-300));">
                            <i class="fas fa-project-diagram text-white text-2xl"></i>
                        </div>
                        <div class="text-4xl font-bold mb-3" style="color: var(--primary-200);">3+</div>
                        <p class="font-medium text-lg" style="color: var(--primary-300);">重点项目案例</p>
                        <p class="text-sm mt-2" style="color: var(--primary-100);">覆盖教育与新能源领域</p>
                    </div>
                    <div class="text-center p-6 rounded-2xl border" style="background: linear-gradient(to bottom right, rgba(241, 143, 1, 0.1), rgba(241, 143, 1, 0.2)); border-color: rgba(241, 143, 1, 0.3);">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style="background: linear-gradient(to bottom right, var(--accent-100), var(--accent-200));">
                            <i class="fas fa-award text-white text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold mb-2" style="color: var(--accent-100);">100%</div>
                        <p class="font-medium" style="color: var(--accent-200);">项目成功率</p>
                        <p class="text-xs mt-1" style="color: var(--accent-100);">按时按质交付</p>
                    </div>
                    <div class="text-center p-6 rounded-2xl border" style="background: linear-gradient(to bottom right, rgba(104, 166, 125, 0.1), rgba(104, 166, 125, 0.2)); border-color: rgba(104, 166, 125, 0.3);">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style="background: linear-gradient(to bottom right, var(--primary-100), var(--primary-200));">
                            <i class="fas fa-chart-line text-white text-xl"></i>
                        </div>
                        <div class="text-3xl font-bold mb-2" style="color: var(--primary-200);">4000万+</div>
                        <p class="font-medium" style="color: var(--primary-300);">项目总价值</p>
                        <p class="text-xs mt-1" style="color: var(--primary-100);">累计合同金额</p>
                    </div>
                </div>
            </div>

            <div class="flex justify-center mb-12 gap-4 flex-wrap">
                <button class="filter-btn active" data-filter="all"><i class="fas fa-layer-group mr-2"></i>全部项目</button>
                <button class="filter-btn" data-filter="education"><i class="fas fa-chalkboard-teacher mr-2"></i>智慧教育</button>
                <button class="filter-btn" data-filter="energy"><i class="fas fa-wind mr-2"></i>新能源</button>
            </div>
            <div id="project-container" class="grid grid-cols-1 lg:grid-cols-2 gap-10">
                <!-- Project data will be injected here by JavaScript -->
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- ========== Education & Skills Section ========== -->
        <section id="education" class="scroll-mt-24 fade-in-section">
            <h2 class="section-title"><i class="section-icon fas fa-graduation-cap"></i>教育与成果</h2>

            <!-- 学习成长时间线 - 新增 -->
            <div class="max-w-5xl mx-auto mb-16">
                <div class="rounded-2xl p-8 border" style="background: linear-gradient(to right, rgba(143, 191, 159, 0.1), rgba(241, 143, 1, 0.1)); border-color: rgba(143, 191, 159, 0.3);">
                    <h3 class="text-xl font-bold text-center mb-6" style="color: var(--primary-300);">
                        <i class="fas fa-timeline mr-2"></i>持续学习与成长轨迹
                    </h3>
                    <div class="flex flex-col md:flex-row items-center justify-between gap-4">
                        <div class="text-center">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2" style="background-color: var(--primary-200);">
                                <i class="fas fa-graduation-cap text-white"></i>
                            </div>
                            <div class="text-sm font-semibold" style="color: var(--primary-300);">2010-2014</div>
                            <div class="text-xs" style="color: var(--primary-200);">本科教育</div>
                        </div>
                        <div class="hidden md:block flex-1 h-0.5" style="background: linear-gradient(to right, var(--primary-100), var(--accent-100));"></div>
                        <div class="text-center">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2" style="background-color: var(--accent-100);">
                                <i class="fas fa-certificate text-white"></i>
                            </div>
                            <div class="text-sm font-semibold" style="color: var(--accent-200);">2014-2017</div>
                            <div class="text-xs" style="color: var(--accent-100);">专业认证</div>
                        </div>
                        <div class="hidden md:block flex-1 h-0.5" style="background: linear-gradient(to right, var(--accent-100), var(--primary-200));"></div>
                        <div class="text-center">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2" style="background-color: var(--primary-300);">
                                <i class="fas fa-lightbulb text-white"></i>
                            </div>
                            <div class="text-sm font-semibold" style="color: var(--primary-300);">2017-至今</div>
                            <div class="text-xs" style="color: var(--primary-200);">技术创新</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                <div class="glass-container rounded-2xl p-10">
                    <h3 class="text-2xl font-bold mb-6" style="color: var(--primary-200);"><i class="fas fa-university mr-3"></i>教育背景</h3>
                    <div class="flex items-center gap-4">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center flex-shrink-0" style="background-color: rgba(143, 191, 159, 0.2);"><i class="fas fa-graduation-cap text-3xl" style="color: var(--primary-200);"></i></div>
                        <div>
                            <p class="font-semibold text-lg" style="color: var(--primary-300);">河南农业大学</p>
                            <p style="color: var(--text-200);">建筑环境与能源工程 (本科)</p>
                            <p class="text-sm" style="color: var(--text-200);">2010 - 2014</p>
                        </div>
                    </div>
                </div>

                <div class="glass-container rounded-2xl p-10">
                    <h3 class="text-2xl font-bold mb-6" style="color: var(--primary-200);"><i class="fas fa-certificate mr-3"></i>资格证书</h3>
                    <div class="flex flex-wrap gap-3">
                        <span class="certification-badge"><i class="fas fa-language"></i>大学英语四级</span>
                        <span class="certification-badge"><i class="fas fa-car"></i>驾驶证C1</span>
                        <span class="certification-badge"><i class="fas fa-microphone"></i>普通话二甲</span>
                        <span class="certification-badge"><i class="fas fa-desktop"></i>计算机二级</span>
                    </div>
                </div>

                <div class="glass-container rounded-2xl p-10">
                    <h3 class="text-2xl font-bold mb-6" style="color: var(--primary-200);"><i class="fas fa-lightbulb mr-3"></i>技术创新</h3>
                    <div class="space-y-4">
                        <div class="p-3 bg-green-50 rounded-lg border-l-4 border-green-400"><h4 class="font-semibold text-sm text-green-800"><i class="fas fa-patent mr-2"></i>专利成果</h4><p class="text-xs text-gray-600">CFD修正算法(发明)、实验室安全预警(实用新型)等</p></div>
                        <div class="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400"><h4 class="font-semibold text-sm text-blue-800"><i class="fas fa-code mr-2"></i>软件著作权</h4><p class="text-xs text-gray-600">教学设备管理系统(2项)、海信智慧教室管理平台1.0</p></div>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- ========== Footer ========== -->
    <footer class="text-white py-20 mt-24" style="background: linear-gradient(to right, var(--primary-300), var(--primary-200), var(--primary-300));">
        <div class="container mx-auto px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-16 mb-16">
                <!-- 联系信息 -->
                <div class="text-center md:text-left">
                    <h3 class="text-2xl font-bold mb-6" style="color: var(--accent-100);">联系方式</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-center md:justify-start gap-3">
                            <i class="fas fa-envelope" style="color: var(--accent-100);"></i>
                            <a href="mailto:<EMAIL>" class="transition-colors" style="color: white;" onmouseover="this.style.color='var(--accent-100)'" onmouseout="this.style.color='white'"><EMAIL></a>
                        </div>
                        <div class="flex items-center justify-center md:justify-start gap-3">
                            <i class="fas fa-phone" style="color: var(--accent-100);"></i>
                            <span>132 6033 5189</span>
                        </div>
                        <div class="flex items-center justify-center md:justify-start gap-3">
                            <i class="fas fa-map-marker-alt" style="color: var(--accent-100);"></i>
                            <span>广州/深圳</span>
                        </div>
                    </div>
                </div>

                <!-- 核心优势 -->
                <div class="text-center">
                    <h3 class="text-2xl font-bold mb-6" style="color: var(--accent-100);">核心优势</h3>
                    <div class="space-y-3">
                        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                            <span class="text-sm font-medium">11年政企数字化销售经验</span>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                            <span class="text-sm font-medium">3家百亿级企业服务经验</span>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                            <span class="text-sm font-medium">AIoT技术商业化落地专家</span>
                        </div>
                    </div>
                </div>

                <!-- 快速导航 -->
                <div class="text-center md:text-right">
                    <h3 class="text-2xl font-bold mb-6" style="color: var(--accent-100);">快速导航</h3>
                    <div class="space-y-3">
                        <a href="#competency" class="block transition-colors" style="color: white;" onmouseover="this.style.color='var(--accent-100)'" onmouseout="this.style.color='white'">核心能力</a>
                        <a href="#career" class="block transition-colors" style="color: white;" onmouseover="this.style.color='var(--accent-100)'" onmouseout="this.style.color='white'">职业生涯</a>
                        <a href="#projects" class="block transition-colors" style="color: white;" onmouseover="this.style.color='var(--accent-100)'" onmouseout="this.style.color='white'">项目亮点</a>
                        <a href="#education" class="block transition-colors" style="color: white;" onmouseover="this.style.color='var(--accent-100)'" onmouseout="this.style.color='white'">教育背景</a>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="border-t border-white/20 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center gap-4">
                    <div class="text-center md:text-left">
                        <p class="text-gray-300 text-sm">
                            © 2024 翟生 - 专业交互式简历 | 政企数字化销售专家
                        </p>
                    </div>
                    <div class="flex gap-4">
                        <button onclick="window.print()" class="px-4 py-2 rounded-lg text-sm font-medium transition-colors" style="background-color: var(--accent-100);" onmouseover="this.style.backgroundColor='var(--accent-200)'" onmouseout="this.style.backgroundColor='var(--accent-100)'">
                            <i class="fas fa-print mr-2"></i>打印简历
                        </button>
                        <button onclick="downloadPDF()" class="px-4 py-2 rounded-lg text-sm font-medium transition-colors" style="background-color: var(--primary-200);" onmouseover="this.style.backgroundColor='var(--primary-300)'" onmouseout="this.style.backgroundColor='var(--primary-200)'">
                            <i class="fas fa-download mr-2"></i>下载PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- ========== Floating Action Buttons ========== -->
    <div class="floating-actions">
        <button class="floating-btn" id="quick-view-btn" aria-label="快速浏览模式" title="快速浏览模式" style="background: linear-gradient(135deg, #6366f1, #8b5cf6);">
            <i class="fas fa-eye"></i>
        </button>
        <button class="floating-btn contact-btn" id="contact-btn" aria-label="联系我" title="联系我">
            <i class="fas fa-envelope"></i>
        </button>
        <button class="floating-btn share-btn" id="share-btn" aria-label="分享简历" title="分享简历">
            <i class="fas fa-share-alt"></i>
        </button>
        <button class="floating-btn back-to-top" id="back-to-top" aria-label="返回顶部" title="返回顶部">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <script>
    // 错误处理和兼容性检查
    window.addEventListener('error', function(e) {
        console.warn('页面错误:', e.message);
    });

    // 检查浏览器兼容性
    function checkBrowserSupport() {
        const isSupported = 'IntersectionObserver' in window &&
                           'requestAnimationFrame' in window &&
                           'addEventListener' in document;

        if (!isSupported) {
            console.warn('浏览器兼容性警告：某些功能可能无法正常工作');
        }
        return isSupported;
    }

    document.addEventListener('DOMContentLoaded', function() {
        // 检查浏览器支持
        checkBrowserSupport();

        // 检查Chart.js加载状态
        function waitForChartJS() {
            return new Promise((resolve) => {
                if (typeof Chart !== 'undefined') {
                    resolve(true);
                    return;
                }

                let attempts = 0;
                const maxAttempts = 20; // 增加等待时间
                const checkInterval = setInterval(() => {
                    attempts++;
                    if (typeof Chart !== 'undefined') {
                        clearInterval(checkInterval);
                        resolve(true);
                    } else if (attempts >= maxAttempts) {
                        clearInterval(checkInterval);
                        resolve(false);
                    }
                }, 250);
            });
        }

        // 隐藏加载动画
        setTimeout(() => {
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 500);
            }
        }, 1000);
        // --- DATA ---
        const careerData = [
            { company: '海信集团有限公司', period: '2021.09 - 2025.06', title: '销售总监', icon: '📺', achievements: ['**渠道网络建设**：统筹区域经销商网络，制定准入**6大维度评估模型**。制定教育/办公领域差异化市场进入策略，主导AI教学场景产品化落地。', '**运营管理体系**：建立信息化产品区域推广体系与项目全生命周期管控机制。搭建销售铁军培养体系，**3年内输送3名区域总监级干部**。', '**业绩**：构建三级分销体系（**5核心+20普通**），区域覆盖率从**32%提升至62%**。签约**5所985高校**，战略储单**583万**。首创高校智慧教室EPCO模式，客户复购率提升至**67%**。项目交付周期从**45天缩短至28天**。'] },
            { company: '北京京师讯飞教育科技有限公司', period: '2017.07 - 2021.09', title: '市场总监', icon: '🧠', achievements: ['**品牌推广体系**：制定"展会+数字化"双轮驱动战略，新媒体矩阵粉丝突破**50万**。建立省级项目投标管理全流程。', '**政企客户开发**：设计教育装备中心业务拓展模型，搭建区级采购单位分层维护体系。', '**渠道生态优化**：创建代理商分级认证机制，实施渠道赋能"铁三角"模式。', '**业绩**：累计中标省级智慧校园项目**11个**，斩获辽宁省教育云平台项目（中标额**1350万**）。签约**4个省级教育装备中心**，发展核心代理商**22家**，渠道销售额**3年增长200%**（1300万→2600万）。'] },
            { company: '三一集团有限公司', period: '2014.07 - 2017.07', title: '解决方案经理', icon: '💨', achievements: ['**技术支撑体系**：构建风电项目全流程技术支撑架构，运用CFD与GIS构建三维风场模型，建立"技术标书五维评估体系"。', '**数字化技术创新**：研发无人机+AI快速勘测技术，设计风机布局智能优化算法，搭建"资源-方案-效益"三位一体决策模型。', '**流程标准化**：制定《风电项目技术标书编制规范》企业标准，开发标准化方案模板库。', '**业绩**：支持**6省**风光储项目，带动主机销售**32.8亿元**。技术方案使某50MW项目IRR提升至**19.7%**。编制的《复杂地形风电开发技术导则》被**5大发电集团**采纳。方案设计效率提升**105%**。'] }
        ];

        const projectData = [
            {
                title: '北京大学光华管理学院智慧教室项目',
                period: '2023.03 - 2023.12',
                role: '区域项目经理',
                category: 'education',
                icon: 'fas fa-university',
                value: '583万',
                impact: { innovation: 95, efficiency: 88, scale: 76, satisfaction: 92 },
                timeline: [
                    { phase: '需求调研', duration: '2023.03-04', status: 'completed' },
                    { phase: '方案设计', duration: '2023.04-06', status: 'completed' },
                    { phase: '系统部署', duration: '2023.06-10', status: 'completed' },
                    { phase: '培训验收', duration: '2023.10-12', status: 'completed' }
                ],
                overview: '**智能化教学空间重构**：针对**7类教学场景**，建立"三阶需求评估体系"，采用ARCS动机模型分析学员交互偏好。',
                contribution: '**硬件架构**：集成VisionBoard三联屏系统（**225英寸电容矩阵**），部署全贴合智慧黑板。**软件生态**：构建"海信智慧教室管理平台1.0"，对接教务系统，实现课堂行为分析与云端资源调用。**赋能体系**：开发"三级培训体系"，覆盖**200+教职工**。',
                results: '**教学效能**：平均互动频次达**42次/课时**（提升超3倍），教师准备时间从**15分钟降至3分钟**。**技术认证**：通过ISO 9241-307认证，硬件MTBF达**50,000小时**。**行业标杆**：项目入选《中国教育信息化白皮书》，带动高校市占率从**17%跃升至29%**。'
            },
            {
                title: '内蒙古乌兰浩特市创客教室建设项目',
                period: '2019.12 - 2020.12',
                role: '销售总监',
                category: 'education',
                icon: 'fas fa-tools',
                value: '2800万',
                impact: { innovation: 82, efficiency: 91, scale: 85, satisfaction: 89 },
                timeline: [
                    { phase: '项目启动', duration: '2019.12-2020.01', status: 'completed' },
                    { phase: '设计施工', duration: '2020.01-08', status: 'completed' },
                    { phase: '设备安装', duration: '2020.08-11', status: 'completed' },
                    { phase: '运营培训', duration: '2020.11-12', status: 'completed' }
                ],
                overview: '**教育空间智能化重构**：建立"双闭环质量管控体系"，对**3所学校**进行差异化定位。',
                contribution: '**核心技术**：集成"三维教学中枢"（智能终端集群+MR虚实融合），配置物联网传感器矩阵与数字化实验仪器。**教学生态**：开发"四维能力培养模型"。**实施突破**：通过BIM建模节约工时，模块化装修压缩改造周期至**17天/间**。',
                results: '**教学成果**：实验数据处理效率提升**80%**，产出获省级科创大赛一等奖**3项**。**运营优化**：教室空间利用率从**62%提升至91%**。**行业标杆**：入选《内蒙古智慧校园建设白皮书》，"五位一体"管理体系被**6所中学**采纳。项目贡献营收**2800万元**。'
            },
            {
                title: '山西华能汾阳100MW风电项目',
                period: '2015.03 - 2015.10',
                role: '解决方案经理',
                category: 'energy',
                icon: 'fas fa-wind',
                value: '32.8亿',
                impact: { innovation: 88, efficiency: 94, scale: 92, satisfaction: 87 },
                timeline: [
                    { phase: '风资源评估', duration: '2015.03-04', status: 'completed' },
                    { phase: '方案优化', duration: '2015.04-06', status: 'completed' },
                    { phase: '技术验证', duration: '2015.06-08', status: 'completed' },
                    { phase: '项目交付', duration: '2015.08-10', status: 'completed' }
                ],
                overview: '**风场全景数字化建模**：构建四维气象预测体系，采用"中尺度WRF+CFD微尺度修正"双引擎系统。',
                contribution: '**智能布局系统**：开发"三维地形-气象-经济"联合优化算法，基于遗传算法迭代**2000+次**模拟。**关键技术**：构建"风电功率预测三级修正系统"。**运维预判**：部署数字孪生系统，关键部件寿命预测精度达**93%**，故障预警提前**72小时**。',
                results: '**预测精度**：发电量预测误差控制在**8.7%**（行业标准15%）。**技术成果**：CFD修正算法获国家发明专利，项目入选《中国风电智能化典型案例集》。**经济效益**：年等效满发小时数提升至**2415h**，IRR达**12.7%**。**行业标杆**："三维选址模型"被**6个省级电网公司**采纳。'
            }
        ];

        // --- RENDER FUNCTIONS ---
        function renderTimeline() {
            const container = document.getElementById('timeline-container');
            if (!container) return;
            container.innerHTML = careerData.map(job => `
                <div class="timeline-item">
                    <div class="timeline-dot"><span class="icon-text">${job.icon}</span></div>
                    <div class="cursor-pointer timeline-header">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                            <h3 class="text-xl font-bold text-blue-700">${job.company}</h3>
                            <span class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full self-start mt-2 sm:mt-0">${job.period}</span>
                        </div>
                        <p class="font-semibold text-lg text-gray-800">${job.title}</p>
                    </div>
                    <div class="job-details mt-4 space-y-3 hidden">
                        ${job.achievements.map(ach => `<p class="text-gray-600 leading-relaxed text-sm">${formatBold(ach)}</p>`).join('')}
                    </div>
                </div>
            `).join('');
        }

        function renderProjects(filter = 'all') {
            const container = document.getElementById('project-container');
            if (!container) return;
            const filtered = projectData.filter(p => filter === 'all' || p.category === filter);
            container.innerHTML = filtered.map(proj => `
                <div class="project-card">
                    <div class="p-6 cursor-pointer project-header">
                        <div class="flex justify-between items-start mb-4">
                            <div class="flex-1 pr-4">
                                <h3 class="text-xl font-bold text-blue-700 mb-2">${proj.title}</h3>
                                <p class="text-gray-500 text-sm">${proj.role} · ${proj.period}</p>
                                <div class="flex items-center gap-4 mt-2">
                                    <span class="text-lg font-bold text-green-600">💰 ${proj.value}</span>
                                    <span class="project-tag"><i class="${proj.icon} mr-2"></i>${proj.category === 'education' ? '智慧教育' : '新能源'}</span>
                                </div>
                            </div>
                            <div class="flex flex-col items-end">
                                <div class="impact-radar mb-4">
                                    <div class="radar-bg"></div>
                                    <div class="radar-fill"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="text-center">
                                            <div class="text-lg font-bold text-blue-600">${Math.round((proj.impact.innovation + proj.impact.efficiency + proj.impact.scale + proj.impact.satisfaction) / 4)}</div>
                                            <div class="text-xs text-gray-500">影响力</div>
                                        </div>
                                    </div>
                                </div>
                                <i class="arrow-icon fas fa-chevron-down text-gray-400 transition-transform duration-300"></i>
                            </div>
                        </div>
                    </div>
                    <div class="project-card-detail">
                        <div class="px-6 pb-6 space-y-6">
                            <!-- 项目时间线 -->
                            <div class="project-timeline">
                                <h4 class="font-semibold mb-3 text-gray-800"><i class="fas fa-clock mr-2 text-blue-500"></i>项目时间线</h4>
                                ${proj.timeline.map(phase => `
                                    <div class="timeline-point">
                                        <div class="flex justify-between items-center">
                                            <span class="font-medium text-gray-700">${phase.phase}</span>
                                            <span class="text-sm text-gray-500">${phase.duration}</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>

                            <!-- 影响力指标 -->
                            <div>
                                <h4 class="font-semibold mb-3 text-gray-800"><i class="fas fa-chart-radar mr-2 text-purple-500"></i>影响力指标</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">创新度</span>
                                        <div class="flex items-center gap-2">
                                            <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                                                <div class="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-1000" style="width: ${proj.impact.innovation}%"></div>
                                            </div>
                                            <span class="text-sm font-semibold text-blue-600">${proj.impact.innovation}%</span>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">效率提升</span>
                                        <div class="flex items-center gap-2">
                                            <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                                                <div class="h-full bg-gradient-to-r from-green-500 to-green-600 rounded-full transition-all duration-1000" style="width: ${proj.impact.efficiency}%"></div>
                                            </div>
                                            <span class="text-sm font-semibold text-green-600">${proj.impact.efficiency}%</span>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">规模影响</span>
                                        <div class="flex items-center gap-2">
                                            <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                                                <div class="h-full bg-gradient-to-r from-purple-500 to-purple-600 rounded-full transition-all duration-1000" style="width: ${proj.impact.scale}%"></div>
                                            </div>
                                            <span class="text-sm font-semibold text-purple-600">${proj.impact.scale}%</span>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">客户满意度</span>
                                        <div class="flex items-center gap-2">
                                            <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                                                <div class="h-full bg-gradient-to-r from-orange-500 to-orange-600 rounded-full transition-all duration-1000" style="width: ${proj.impact.satisfaction}%"></div>
                                            </div>
                                            <span class="text-sm font-semibold text-orange-600">${proj.impact.satisfaction}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 项目详情 -->
                            <div><h4 class="font-semibold mb-1 text-gray-800"><i class="fas fa-clipboard-list mr-2 text-blue-500"></i>项目概述</h4><p class="text-sm text-gray-600">${formatBold(proj.overview)}</p></div>
                            <div><h4 class="font-semibold mb-1 text-gray-800"><i class="fas fa-cogs mr-2 text-green-500"></i>核心贡献</h4><p class="text-sm text-gray-600">${formatBold(proj.contribution)}</p></div>
                            <div><h4 class="font-semibold mb-1 text-gray-800"><i class="fas fa-chart-line mr-2 text-purple-500"></i>项目成果</h4><p class="text-sm text-gray-600">${formatBold(proj.results)}</p></div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // --- UTILITY FUNCTIONS ---
        function formatBold(text) {
            return text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        }

        // --- EVENT LISTENERS & INITIALIZATION ---
        function initEventListeners() {
            // Timeline expand/collapse
            document.getElementById('timeline-container')?.addEventListener('click', e => {
                const header = e.target.closest('.timeline-header');
                if (header) {
                    const details = header.nextElementSibling;
                    details.classList.toggle('hidden');
                }
            });

            // Project expand/collapse
            document.getElementById('project-container')?.addEventListener('click', e => {
                const header = e.target.closest('.project-header');
                if (header) {
                    const detail = header.nextElementSibling;
                    const arrow = header.querySelector('.arrow-icon');
                    if (detail.style.maxHeight && detail.style.maxHeight !== '0px') {
                        detail.style.maxHeight = '0px';
                        arrow.classList.remove('rotate-180');
                    } else {
                        detail.style.maxHeight = detail.scrollHeight + "px";
                        arrow.classList.add('rotate-180');
                    }
                }
            });

            // Project filtering
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelector('.filter-btn.active').classList.remove('active');
                    this.classList.add('active');
                    renderProjects(this.dataset.filter);
                });
            });

            // Navbar scroll effect
            const navbar = document.getElementById('navbar');
            window.addEventListener('scroll', () => {
                navbar.classList.toggle('scrolled', window.scrollY > 50);
            });

            // Back to top button
            const backToTopBtn = document.getElementById('back-to-top');
            window.addEventListener('scroll', () => {
                backToTopBtn.classList.toggle('visible', window.scrollY > 300);
            });
            backToTopBtn.addEventListener('click', () => window.scrollTo({ top: 0, behavior: 'smooth' }));

            // Smooth scroll for nav links
            document.querySelectorAll('nav a.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    document.querySelector(targetId)?.scrollIntoView({ behavior: 'smooth' });
                });
            });
        }

        // --- ANIMATIONS ---
        function initAnimations() {
            // 性能优化的Intersection Observer
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '50px 0px -50px 0px'
            };

            // Section fade-in on scroll with performance optimization
            const observer = new IntersectionObserver((entries, obs) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 使用requestAnimationFrame优化动画性能
                        requestAnimationFrame(() => {
                            entry.target.classList.add('is-visible');
                            // Animate counters if in competency section
                            if (entry.target.id === 'competency') {
                                animateCounters(entry.target);
                            }
                            // 懒加载图表
                            if (entry.target.id === 'competency' && !entry.target.dataset.chartsLoaded) {
                                initSkillsChart();
                                entry.target.dataset.chartsLoaded = 'true';
                            }
                        });
                        obs.unobserve(entry.target);
                    }
                });
            }, observerOptions);
            document.querySelectorAll('.fade-in-section').forEach(section => observer.observe(section));

            // Nav link highlighting on scroll with progress indicator
            const navObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const activeLink = document.querySelector(`nav a.nav-link[href="#${entry.target.id}"]`);
                        document.querySelectorAll('nav a.nav-link').forEach(link => {
                            link.classList.remove('active');
                        });
                        if (activeLink) {
                            activeLink.classList.add('active');
                            updateNavProgress(activeLink);
                        }
                    }
                });
            }, { rootMargin: '-40% 0px -60% 0px' });
            document.querySelectorAll('section[id]').forEach(sec => navObserver.observe(sec));

            // Update navigation progress indicator
            function updateNavProgress(activeLink) {
                const navProgress = document.getElementById('nav-progress');
                const navMenu = document.querySelector('.nav-menu');
                if (navProgress && activeLink && navMenu) {
                    const linkRect = activeLink.getBoundingClientRect();
                    const menuRect = navMenu.getBoundingClientRect();
                    const left = linkRect.left - menuRect.left;
                    const width = linkRect.width;

                    navProgress.style.left = left + 'px';
                    navProgress.style.width = width + 'px';
                }
            }
        }

        function animateCounters(section) {
            const counters = section.querySelectorAll('.number-counter[data-target]');
            counters.forEach(counter => {
                const target = +counter.dataset.target;
                const suffix = counter.dataset.suffix || '';
                const decimals = counter.dataset.decimal ? +counter.dataset.decimal : 0;
                let current = 0;
                const duration = 2000;
                const stepTime = 20;
                const steps = duration / stepTime;
                const increment = target / steps;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        clearInterval(timer);
                        counter.textContent = target.toFixed(decimals) + suffix;
                    } else {
                        counter.textContent = current.toFixed(decimals) + suffix;
                    }
                }, stepTime);
            });

            // Progress bars
            section.querySelectorAll('.progress-fill').forEach(bar => {
                const progress = bar.dataset.progress || '0';
                setTimeout(() => { bar.style.width = progress + '%'; }, 300);
            });

            // 环形进度图动画
            section.querySelectorAll('.progress-circle').forEach(circle => {
                const progress = +circle.dataset.progress;
                const circumference = 2 * Math.PI * 36; // r=36
                const offset = circumference - (progress / 100) * circumference;
                setTimeout(() => {
                    circle.style.strokeDashoffset = offset;
                }, 500);
            });

            // 柱状图动画
            section.querySelectorAll('.bar').forEach((bar, index) => {
                const height = bar.dataset.height || '50';
                setTimeout(() => {
                    bar.style.height = height + '%';
                }, 300 + index * 100);
            });
        }

        // --- SKILLS RADAR CHART ---
        function initSkillsChart() {
            const ctx = document.getElementById('skillsRadarChart');
            if (!ctx) {
                console.warn('雷达图canvas元素未找到');
                return;
            }

            // 多重检查Chart.js是否加载成功
            function checkChartJS() {
                return typeof Chart !== 'undefined' && Chart.version;
            }

            // 创建备用的静态雷达图
            function createFallbackRadarChart() {
                const container = ctx.parentElement;
                if (!container) return;

                container.innerHTML = `
                    <div class="text-center">
                        <div class="relative mx-auto mb-4" style="width: 200px; height: 200px;">
                            <svg viewBox="0 0 200 200" class="w-full h-full">
                                <!-- 背景网格 -->
                                <g stroke="#e5e7eb" stroke-width="1" fill="none">
                                    <circle cx="100" cy="100" r="80"/>
                                    <circle cx="100" cy="100" r="60"/>
                                    <circle cx="100" cy="100" r="40"/>
                                    <circle cx="100" cy="100" r="20"/>
                                    <line x1="100" y1="20" x2="100" y2="180"/>
                                    <line x1="20" y1="100" x2="180" y2="100"/>
                                    <line x1="41.7" y1="41.7" x2="158.3" y2="158.3"/>
                                    <line x1="158.3" y1="41.7" x2="41.7" y2="158.3"/>
                                </g>
                                <!-- 数据多边形 -->
                                <polygon points="100,24 156,50 172,124 124,164 44,140 28,76"
                                         fill="rgba(37, 99, 235, 0.2)"
                                         stroke="rgba(37, 99, 235, 1)"
                                         stroke-width="2"/>
                                <!-- 数据点 -->
                                <circle cx="100" cy="24" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                                <circle cx="156" cy="50" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                                <circle cx="172" cy="124" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                                <circle cx="124" cy="164" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                                <circle cx="44" cy="140" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                                <circle cx="28" cy="76" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
                            <div>销售管理: 95%</div>
                            <div>项目管理: 90%</div>
                            <div>技术理解: 85%</div>
                            <div>团队领导: 92%</div>
                            <div>客户关系: 96%</div>
                            <div>战略规划: 88%</div>
                        </div>
                    </div>
                `;
            }

            // 尝试初始化Chart.js雷达图
            function tryInitChart() {
                if (!checkChartJS()) {
                    console.warn('Chart.js 未加载，使用备用雷达图');
                    createFallbackRadarChart();
                    return;
                }

                try {
                    // 确保canvas有正确的尺寸
                    ctx.width = 300;
                    ctx.height = 300;

                    const chart = new Chart(ctx, {
                        type: 'radar',
                        data: {
                            labels: ['销售管理', '项目管理', '技术理解', '团队领导', '客户关系', '战略规划'],
                            datasets: [{
                                label: '技能水平',
                                data: [95, 90, 85, 92, 96, 88],
                                backgroundColor: 'rgba(37, 99, 235, 0.2)',
                                borderColor: 'rgba(37, 99, 235, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(245, 158, 11, 1)',
                                pointBorderColor: '#fff',
                                pointBorderWidth: 2,
                                pointRadius: 6
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false }
                            },
                            scales: {
                                r: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                        stepSize: 20,
                                        color: '#6b7280',
                                        font: { size: 10 },
                                        display: false
                                    },
                                    grid: { color: 'rgba(107, 114, 128, 0.3)' },
                                    pointLabels: {
                                        color: '#374151',
                                        font: { size: 11, weight: '600' }
                                    }
                                }
                            }
                        }
                    });

                    console.log('雷达图初始化成功');
                    return chart;
                } catch (error) {
                    console.warn('Chart.js雷达图初始化失败:', error);
                    createFallbackRadarChart();
                }
            }

            // 延迟初始化，确保Chart.js完全加载
            if (checkChartJS()) {
                tryInitChart();
            } else {
                // 等待Chart.js加载
                let attempts = 0;
                const maxAttempts = 10;
                const checkInterval = setInterval(() => {
                    attempts++;
                    if (checkChartJS()) {
                        clearInterval(checkInterval);
                        tryInitChart();
                    } else if (attempts >= maxAttempts) {
                        clearInterval(checkInterval);
                        console.warn('Chart.js加载超时，使用备用雷达图');
                        createFallbackRadarChart();
                    }
                }, 500);
            }
        }

        // --- ENHANCED INTERACTIONS ---
        function initEnhancedFeatures() {
            // 阅读进度条
            const progressBar = document.getElementById('reading-progress');
            function updateReadingProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.documentElement.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                if (progressBar) {
                    progressBar.style.width = scrollPercent + '%';
                }
            }

            // 视差滚动效果
            function updateParallax() {
                const scrolled = window.pageYOffset;
                const parallaxElements = document.querySelectorAll('.parallax-bg');
                parallaxElements.forEach(element => {
                    const speed = element.dataset.speed || 0.5;
                    element.style.transform = `translateY(${scrolled * speed}px)`;
                });
            }

            // 浮动按钮显示/隐藏
            const floatingBtns = document.querySelectorAll('.floating-btn');

            window.addEventListener('scroll', () => {
                updateReadingProgress();
                updateParallax();

                const shouldShow = window.scrollY > 300;
                floatingBtns.forEach((btn, index) => {
                    setTimeout(() => {
                        btn.classList.toggle('visible', shouldShow);
                    }, index * 100);
                });
            });

            // 联系按钮
            document.getElementById('contact-btn')?.addEventListener('click', () => {
                window.location.href = 'mailto:<EMAIL>?subject=简历咨询&body=您好，我对您的简历很感兴趣，希望进一步了解...';
            });

            // 分享按钮
            document.getElementById('share-btn')?.addEventListener('click', async () => {
                if (navigator.share) {
                    try {
                        await navigator.share({
                            title: '翟生 - 专业交互式简历',
                            text: '政企数字化销售专家，11年经验，专注AIoT技术商业化落地',
                            url: window.location.href
                        });
                    } catch (err) {
                        copyToClipboard(window.location.href);
                    }
                } else {
                    copyToClipboard(window.location.href);
                }
            });

            // 快速浏览模式
            let isQuickViewMode = false;
            document.getElementById('quick-view-btn')?.addEventListener('click', () => {
                isQuickViewMode = !isQuickViewMode;
                const body = document.body;
                const btn = document.getElementById('quick-view-btn');

                if (isQuickViewMode) {
                    body.classList.add('quick-view-mode');
                    btn.innerHTML = '<i class="fas fa-eye-slash"></i>';
                    btn.title = '退出快速浏览';
                    showNotification('已开启快速浏览模式，显示关键信息', 'info');
                } else {
                    body.classList.remove('quick-view-mode');
                    btn.innerHTML = '<i class="fas fa-eye"></i>';
                    btn.title = '快速浏览模式';
                    showNotification('已退出快速浏览模式', 'info');
                }
            });

            // 移除主题切换功能，专注亮色模式体验
        }

        // 移除主题切换相关函数

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('链接已复制到剪贴板！');
            });
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' : 'bg-blue-500';
            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-all duration-300 backdrop-blur-sm`;
            notification.innerHTML = `
                <div class="flex items-center gap-3">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // PDF下载功能
        window.downloadPDF = function() {
            showNotification('PDF下载功能需要后端支持，当前为演示版本');
        };

        // --- MAIN EXECUTION ---
        renderTimeline();
        renderProjects('all');
        initEventListeners();
        initAnimations();

        // 异步初始化图表，确保Chart.js完全加载
        waitForChartJS().then((chartLoaded) => {
            if (chartLoaded) {
                console.log('Chart.js加载成功，初始化雷达图');
            } else {
                console.warn('Chart.js加载失败，将使用备用图表');
            }
            initSkillsChart();
        });

        initEnhancedFeatures();

        // Add rotate class for project arrows
        const style = document.createElement('style');
        style.textContent = `.rotate-180 { transform: rotate(180deg); }`;
        document.head.appendChild(style);

        // 性能优化：预加载关键资源和懒加载
        function initPerformanceOptimizations() {
            // 预加载关键资源
            const preloadLinks = [
                'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Noto+Sans+SC:wght@300;400;500;600;700;800&display=swap',
                'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
            ];

            preloadLinks.forEach(href => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'style';
                link.href = href;
                document.head.appendChild(link);
            });

            // 图片懒加载
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            observer.unobserve(img);
                        }
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });

            // 动画性能优化
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
            if (prefersReducedMotion.matches) {
                document.body.classList.add('reduce-motion');
            }

            // 防抖滚动事件
            let scrollTimeout;
            const originalScrollHandler = window.onscroll;
            window.addEventListener('scroll', () => {
                if (scrollTimeout) {
                    cancelAnimationFrame(scrollTimeout);
                }
                scrollTimeout = requestAnimationFrame(() => {
                    if (originalScrollHandler) originalScrollHandler();
                });
            }, { passive: true });
        }

        initPerformanceOptimizations();

        // 添加键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // 关闭所有展开的项目详情
                document.querySelectorAll('.project-card-detail').forEach(detail => {
                    if (detail.style.maxHeight && detail.style.maxHeight !== '0px') {
                        detail.style.maxHeight = '0px';
                        const arrow = detail.previousElementSibling.querySelector('.arrow-icon');
                        if (arrow) arrow.classList.remove('rotate-180');
                    }
                });
            }
        });
    });
    </script>
</body>
</html>