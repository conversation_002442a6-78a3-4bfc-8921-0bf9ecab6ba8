<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js 雷达图测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chart-container {
            width: 400px;
            height: 400px;
            margin: 20px auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Chart.js 雷达图测试</h1>
    
    <div id="status" class="status info">正在检测 Chart.js...</div>
    
    <div class="chart-container">
        <canvas id="testRadarChart" width="300" height="300"></canvas>
    </div>
    
    <div id="fallback" style="display: none;">
        <h3>备用静态雷达图</h3>
        <div style="text-align: center;">
            <svg viewBox="0 0 200 200" style="width: 200px; height: 200px; border: 1px solid #ddd;">
                <!-- 背景网格 -->
                <g stroke="#e5e7eb" stroke-width="1" fill="none">
                    <circle cx="100" cy="100" r="80"/>
                    <circle cx="100" cy="100" r="60"/>
                    <circle cx="100" cy="100" r="40"/>
                    <circle cx="100" cy="100" r="20"/>
                    <line x1="100" y1="20" x2="100" y2="180"/>
                    <line x1="20" y1="100" x2="180" y2="100"/>
                    <line x1="41.7" y1="41.7" x2="158.3" y2="158.3"/>
                    <line x1="158.3" y1="41.7" x2="41.7" y2="158.3"/>
                </g>
                <!-- 数据多边形 -->
                <polygon points="100,24 156,50 172,124 124,164 44,140 28,76" 
                         fill="rgba(37, 99, 235, 0.2)" 
                         stroke="rgba(37, 99, 235, 1)" 
                         stroke-width="2"/>
                <!-- 数据点 -->
                <circle cx="100" cy="24" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                <circle cx="156" cy="50" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                <circle cx="172" cy="124" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                <circle cx="124" cy="164" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                <circle cx="44" cy="140" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
                <circle cx="28" cy="76" r="4" fill="rgba(245, 158, 11, 1)" stroke="#fff" stroke-width="2"/>
            </svg>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                销售管理: 95% | 项目管理: 90% | 技术理解: 85%<br>
                团队领导: 92% | 客户关系: 96% | 战略规划: 88%
            </div>
        </div>
    </div>

    <script>
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function checkChartJS() {
            if (typeof Chart === 'undefined') {
                updateStatus('Chart.js 未加载', 'error');
                return false;
            }
            updateStatus(`Chart.js 已加载 (版本: ${Chart.version || '未知'})`, 'success');
            return true;
        }

        function createRadarChart() {
            const ctx = document.getElementById('testRadarChart');
            if (!ctx) {
                updateStatus('Canvas 元素未找到', 'error');
                return;
            }

            try {
                const chart = new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: ['销售管理', '项目管理', '技术理解', '团队领导', '客户关系', '战略规划'],
                        datasets: [{
                            label: '技能水平',
                            data: [95, 90, 85, 92, 96, 88],
                            backgroundColor: 'rgba(37, 99, 235, 0.2)',
                            borderColor: 'rgba(37, 99, 235, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(245, 158, 11, 1)',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            r: {
                                beginAtZero: true,
                                max: 100,
                                ticks: {
                                    stepSize: 20,
                                    color: '#6b7280',
                                    font: { size: 10 }
                                },
                                grid: { color: 'rgba(107, 114, 128, 0.3)' },
                                pointLabels: {
                                    color: '#374151',
                                    font: { size: 11, weight: '600' }
                                }
                            }
                        }
                    }
                });
                updateStatus('雷达图创建成功！', 'success');
                return chart;
            } catch (error) {
                updateStatus(`雷达图创建失败: ${error.message}`, 'error');
                document.getElementById('fallback').style.display = 'block';
                return null;
            }
        }

        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 检查Chart.js是否加载
            if (checkChartJS()) {
                createRadarChart();
            } else {
                // 等待Chart.js加载
                let attempts = 0;
                const maxAttempts = 10;
                const checkInterval = setInterval(() => {
                    attempts++;
                    if (checkChartJS()) {
                        clearInterval(checkInterval);
                        createRadarChart();
                    } else if (attempts >= maxAttempts) {
                        clearInterval(checkInterval);
                        updateStatus('Chart.js 加载超时，显示备用图表', 'error');
                        document.getElementById('fallback').style.display = 'block';
                    }
                }, 500);
            }
        });
    </script>
</body>
</html>
